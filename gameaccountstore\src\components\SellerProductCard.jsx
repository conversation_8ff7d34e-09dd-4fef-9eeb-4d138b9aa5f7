import React, { useState } from 'react';
import { doc, deleteDoc } from 'firebase/firestore';
import { db } from '../config/firebase';
import './SellerProductCard.css';

const SellerProductCard = ({ product, onEdit }) => {
  const [deleting, setDeleting] = useState(false);

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this product?')) {
      return;
    }

    setDeleting(true);
    try {
      await deleteDoc(doc(db, 'products', product.id));
    } catch (error) {
      console.error('Error deleting product:', error);
      alert('Failed to delete product. Please try again.');
    } finally {
      setDeleting(false);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  return (
    <div className="seller-product-card">
      <div className="product-image">
        {product.screenshots && product.screenshots.length > 0 ? (
          <img 
            src={product.screenshots[0]} 
            alt={product.title}
            onError={(e) => {
              e.target.src = '/placeholder-image.jpg';
            }}
          />
        ) : (
          <div className="placeholder-image">
            <span>No Image</span>
          </div>
        )}
        <div className={`status-overlay ${product.status}`}>
          {product.status === 'available' ? 'Available' : 'Sold'}
        </div>
      </div>
      
      <div className="product-info">
        <h3 className="product-title">{product.title}</h3>
        <p className="product-game">{product.gameName}</p>
        <p className="product-description">
          {product.description.length > 80 
            ? `${product.description.substring(0, 80)}...` 
            : product.description
          }
        </p>
        
        <div className="product-meta">
          <div className="product-price">
            {formatPrice(product.price)}
          </div>
          <div className="product-date">
            Listed {formatDate(product.createdAt)}
          </div>
        </div>
        
        <div className="product-actions">
          <button 
            className="edit-btn"
            onClick={() => onEdit(product)}
          >
            Edit
          </button>
          <button 
            className="delete-btn"
            onClick={handleDelete}
            disabled={deleting}
          >
            {deleting ? 'Deleting...' : 'Delete'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SellerProductCard;
