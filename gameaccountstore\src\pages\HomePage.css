.home-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.home-header {
  text-align: center;
  margin-bottom: 3rem;
}

.home-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.home-header p {
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
}

.products-section {
  margin-top: 2rem;
}

.products-header {
  margin-bottom: 1.5rem;
}

.products-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.no-products {
  text-align: center;
  padding: 3rem 1rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.no-products h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.no-products p {
  color: #6b7280;
  margin: 0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner {
  font-size: 1.125rem;
  color: #4f46e5;
  font-weight: 500;
}

.error-container {
  text-align: center;
  padding: 3rem 1rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #dc2626;
}

.error-container h2 {
  color: #dc2626;
  margin: 0 0 1rem 0;
}

.error-container p {
  color: #6b7280;
  margin: 0;
}

@media (max-width: 768px) {
  .home-page {
    padding: 1rem;
  }
  
  .home-header h1 {
    font-size: 2rem;
  }
  
  .home-header p {
    font-size: 1rem;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
