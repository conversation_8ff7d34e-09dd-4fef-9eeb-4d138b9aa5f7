import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { signOut } from 'firebase/auth';
import { useAuth } from '../context/AuthContext';
import { auth } from '../config/firebase';
import './Navbar.css';

const Navbar = () => {
  const { user, userRole } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await signOut(auth);
      navigate('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <nav className="navbar">
      <div className="navbar-container">
        <Link to="/" className="navbar-brand">
          GameAccountStore
        </Link>
        
        <div className="navbar-menu">
          <Link to="/" className="navbar-item">
            Home
          </Link>
          
          {user ? (
            <>
              {userRole === 'seller' && (
                <Link to="/seller-dashboard" className="navbar-item">
                  Seller Dashboard
                </Link>
              )}
              
              {userRole === 'admin' && (
                <Link to="/admin-dashboard" className="navbar-item">
                  Admin Dashboard
                </Link>
              )}
              
              <div className="navbar-user">
                <span className="user-email">{user.email}</span>
                <span className="user-role">({userRole})</span>
                <button onClick={handleLogout} className="logout-btn">
                  Logout
                </button>
              </div>
            </>
          ) : (
            <div className="navbar-auth">
              <Link to="/login" className="navbar-item">
                Login
              </Link>
              <Link to="/register" className="navbar-item register-btn">
                Register
              </Link>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
