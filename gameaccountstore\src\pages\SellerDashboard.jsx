import React, { useEffect, useState } from 'react';
import { collection, query, where, orderBy, onSnapshot } from 'firebase/firestore';
import { db } from '../config/firebase';
import { useAuth } from '../context/AuthContext';
import ProductForm from '../components/ProductForm';
import SellerProductCard from '../components/SellerProductCard';
import './SellerDashboard.css';

const SellerDashboard = () => {
  const { user } = useAuth();
  const [products, setProducts] = useState([]);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showProductForm, setShowProductForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);

  useEffect(() => {
    if (!user) return;

    // Fetch seller's products
    const productsQuery = query(
      collection(db, 'products'),
      where('sellerId', '==', user.uid),
      orderBy('createdAt', 'desc')
    );

    const unsubscribeProducts = onSnapshot(productsQuery, (snapshot) => {
      const productsData = [];
      snapshot.forEach((doc) => {
        productsData.push({ id: doc.id, ...doc.data() });
      });
      setProducts(productsData);
      setLoading(false);
    });

    // Fetch seller's orders
    const ordersQuery = query(
      collection(db, 'orders'),
      where('sellerId', '==', user.uid),
      orderBy('purchaseDate', 'desc')
    );

    const unsubscribeOrders = onSnapshot(ordersQuery, (snapshot) => {
      const ordersData = [];
      snapshot.forEach((doc) => {
        ordersData.push({ id: doc.id, ...doc.data() });
      });
      setOrders(ordersData);
    });

    return () => {
      unsubscribeProducts();
      unsubscribeOrders();
    };
  }, [user]);

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setShowProductForm(true);
  };

  const handleCloseForm = () => {
    setShowProductForm(false);
    setEditingProduct(null);
  };

  const calculateStats = () => {
    const totalProducts = products.length;
    const availableProducts = products.filter(p => p.status === 'available').length;
    const soldProducts = products.filter(p => p.status === 'sold').length;
    const totalRevenue = orders.reduce((sum, order) => sum + order.amount, 0);

    return {
      totalProducts,
      availableProducts,
      soldProducts,
      totalRevenue
    };
  };

  const stats = calculateStats();

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">Loading dashboard...</div>
      </div>
    );
  }

  return (
    <div className="seller-dashboard">
      <div className="dashboard-header">
        <h1>Seller Dashboard</h1>
        <button 
          className="add-product-btn"
          onClick={() => setShowProductForm(true)}
        >
          Add New Product
        </button>
      </div>

      <div className="dashboard-stats">
        <div className="stat-card">
          <h3>Total Products</h3>
          <div className="stat-value">{stats.totalProducts}</div>
        </div>
        <div className="stat-card">
          <h3>Available</h3>
          <div className="stat-value available">{stats.availableProducts}</div>
        </div>
        <div className="stat-card">
          <h3>Sold</h3>
          <div className="stat-value sold">{stats.soldProducts}</div>
        </div>
        <div className="stat-card">
          <h3>Total Revenue</h3>
          <div className="stat-value revenue">
            ${stats.totalRevenue.toFixed(2)}
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="products-section">
          <h2>Your Products</h2>
          {products.length === 0 ? (
            <div className="no-products">
              <p>You haven't listed any products yet.</p>
              <button 
                className="add-first-product-btn"
                onClick={() => setShowProductForm(true)}
              >
                Add Your First Product
              </button>
            </div>
          ) : (
            <div className="products-grid">
              {products.map((product) => (
                <SellerProductCard 
                  key={product.id} 
                  product={product}
                  onEdit={handleEditProduct}
                />
              ))}
            </div>
          )}
        </div>

        <div className="orders-section">
          <h2>Recent Orders</h2>
          {orders.length === 0 ? (
            <div className="no-orders">
              <p>No orders yet.</p>
            </div>
          ) : (
            <div className="orders-list">
              {orders.slice(0, 5).map((order) => (
                <div key={order.id} className="order-item">
                  <div className="order-info">
                    <h4>{order.productTitle}</h4>
                    <p>Buyer: {order.buyerId}</p>
                    <p>Amount: ${order.amount.toFixed(2)}</p>
                  </div>
                  <div className="order-date">
                    {order.purchaseDate.toDate().toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {showProductForm && (
        <ProductForm 
          product={editingProduct}
          onClose={handleCloseForm}
        />
      )}
    </div>
  );
};

export default SellerDashboard;
