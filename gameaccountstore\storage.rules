rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return request.auth.token.role;
    }
    
    function isSeller() {
      return isAuthenticated() && getUserRole() == 'seller';
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserRole() == 'admin';
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*');
    }
    
    function isValidVideoFile() {
      return request.resource.contentType.matches('video/.*');
    }
    
    function isValidFileSize() {
      // Max 10MB for images, 50MB for videos
      return (isValidImageFile() && request.resource.size <= 10 * 1024 * 1024) ||
             (isValidVideoFile() && request.resource.size <= 50 * 1024 * 1024);
    }
    
    // Product files (screenshots and videos)
    match /products/{userId}/{fileName} {
      // Sellers can upload files to their own folder
      allow create: if isSeller() && 
        isOwner(userId) && 
        (isValidImageFile() || isValidVideoFile()) &&
        isValidFileSize();
      
      // Anyone can read product files (for viewing products)
      allow read: if true;
      
      // Sellers can update their own files
      allow update: if isSeller() && 
        isOwner(userId) &&
        (isValidImageFile() || isValidVideoFile()) &&
        isValidFileSize();
      
      // Sellers can delete their own files
      allow delete: if isSeller() && isOwner(userId);
      
      // Admins can delete any files
      allow delete: if isAdmin();
    }
    
    // User profile pictures (optional feature)
    match /users/{userId}/profile/{fileName} {
      // Users can upload their own profile pictures
      allow create, update: if isOwner(userId) && 
        isValidImageFile() &&
        request.resource.size <= 5 * 1024 * 1024; // 5MB limit
      
      // Anyone can read profile pictures
      allow read: if true;
      
      // Users can delete their own profile pictures
      allow delete: if isOwner(userId);
      
      // Admins can delete any profile pictures
      allow delete: if isAdmin();
    }
    
    // Deny all other operations
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
