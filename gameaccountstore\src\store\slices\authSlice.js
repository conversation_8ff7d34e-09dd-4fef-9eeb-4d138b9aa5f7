import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  user: null,
  userRole: null,
  loading: true,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
      state.loading = false;
      state.error = null;
    },
    setUserRole: (state, action) => {
      state.userRole = action.payload;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    },
    logout: (state) => {
      state.user = null;
      state.userRole = null;
      state.loading = false;
      state.error = null;
    },
  },
});

export const { setUser, setUserRole, setLoading, setError, logout } = authSlice.actions;
export default authSlice.reducer;
