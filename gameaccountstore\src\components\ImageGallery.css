.image-gallery {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.main-media {
  width: 100%;
  height: 400px;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-image,
.main-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-media {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 1rem;
}

.media-thumbnails {
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
  padding: 0.5rem 0;
}

.thumbnail {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 0.375rem;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;
}

.thumbnail:hover {
  border-color: #d1d5db;
}

.thumbnail.active {
  border-color: #4f46e5;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  background-color: #1f2937;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
}

.video-thumbnail span:first-child {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

@media (max-width: 768px) {
  .main-media {
    height: 300px;
  }
  
  .thumbnail {
    width: 60px;
    height: 60px;
  }
}
