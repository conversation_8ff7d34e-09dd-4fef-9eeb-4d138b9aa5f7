import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { collection, query, where, orderBy, onSnapshot } from 'firebase/firestore';
import { db } from '../config/firebase';
import { setProducts, setLoading, setError, setFilters } from '../store/slices/productsSlice';
import ProductCard from '../components/ProductCard';
import ProductFilters from '../components/ProductFilters';
import './HomePage.css';

const HomePage = () => {
  const dispatch = useDispatch();
  const { products, loading, error, filters } = useSelector((state) => state.products);
  const [filteredProducts, setFilteredProducts] = useState([]);

  useEffect(() => {
    dispatch(setLoading(true));
    
    // Create query for available products
    const q = query(
      collection(db, 'products'),
      where('status', '==', 'available'),
      orderBy(filters.sortBy, filters.sortOrder)
    );

    // Set up real-time listener
    const unsubscribe = onSnapshot(
      q,
      (querySnapshot) => {
        const productsData = [];
        querySnapshot.forEach((doc) => {
          productsData.push({
            id: doc.id,
            ...doc.data()
          });
        });
        dispatch(setProducts(productsData));
      },
      (error) => {
        console.error('Error fetching products:', error);
        dispatch(setError('Failed to load products'));
      }
    );

    return () => unsubscribe();
  }, [dispatch, filters.sortBy, filters.sortOrder]);

  useEffect(() => {
    // Apply client-side filtering
    let filtered = products;

    if (filters.gameName) {
      filtered = filtered.filter(product =>
        product.gameName.toLowerCase().includes(filters.gameName.toLowerCase())
      );
    }

    if (filters.priceRange.min > 0 || filters.priceRange.max < 1000) {
      filtered = filtered.filter(product =>
        product.price >= filters.priceRange.min && product.price <= filters.priceRange.max
      );
    }

    setFilteredProducts(filtered);
  }, [products, filters]);

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">Loading products...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <h2>Error</h2>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="home-page">
      <div className="home-header">
        <h1>Game Account Marketplace</h1>
        <p>Find and purchase premium game accounts</p>
      </div>

      <ProductFilters />

      <div className="products-section">
        <div className="products-header">
          <h2>Available Accounts ({filteredProducts.length})</h2>
        </div>

        {filteredProducts.length === 0 ? (
          <div className="no-products">
            <h3>No products found</h3>
            <p>Try adjusting your filters or check back later for new listings.</p>
          </div>
        ) : (
          <div className="products-grid">
            {filteredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default HomePage;
