import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setFilters } from '../store/slices/productsSlice';
import './ProductFilters.css';

const ProductFilters = () => {
  const dispatch = useDispatch();
  const { filters } = useSelector((state) => state.products);
  const [showFilters, setShowFilters] = useState(false);

  const handleFilterChange = (filterType, value) => {
    dispatch(setFilters({ [filterType]: value }));
  };

  const handlePriceRangeChange = (type, value) => {
    const newPriceRange = {
      ...filters.priceRange,
      [type]: parseInt(value) || 0
    };
    dispatch(setFilters({ priceRange: newPriceRange }));
  };

  const resetFilters = () => {
    dispatch(setFilters({
      gameName: '',
      priceRange: { min: 0, max: 1000 },
      sortBy: 'createdAt',
      sortOrder: 'desc',
    }));
  };

  return (
    <div className="product-filters">
      <div className="filters-header">
        <button 
          className="toggle-filters-btn"
          onClick={() => setShowFilters(!showFilters)}
        >
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </button>
        <button className="reset-filters-btn" onClick={resetFilters}>
          Reset Filters
        </button>
      </div>

      <div className={`filters-content ${showFilters ? 'show' : ''}`}>
        <div className="filter-group">
          <label htmlFor="gameName">Game Name:</label>
          <input
            type="text"
            id="gameName"
            value={filters.gameName}
            onChange={(e) => handleFilterChange('gameName', e.target.value)}
            placeholder="Search by game name..."
          />
        </div>

        <div className="filter-group">
          <label>Price Range:</label>
          <div className="price-range">
            <input
              type="number"
              value={filters.priceRange.min}
              onChange={(e) => handlePriceRangeChange('min', e.target.value)}
              placeholder="Min"
              min="0"
            />
            <span>to</span>
            <input
              type="number"
              value={filters.priceRange.max}
              onChange={(e) => handlePriceRangeChange('max', e.target.value)}
              placeholder="Max"
              min="0"
            />
          </div>
        </div>

        <div className="filter-group">
          <label htmlFor="sortBy">Sort By:</label>
          <select
            id="sortBy"
            value={filters.sortBy}
            onChange={(e) => handleFilterChange('sortBy', e.target.value)}
          >
            <option value="createdAt">Date Listed</option>
            <option value="price">Price</option>
            <option value="title">Title</option>
            <option value="gameName">Game Name</option>
          </select>
        </div>

        <div className="filter-group">
          <label htmlFor="sortOrder">Order:</label>
          <select
            id="sortOrder"
            value={filters.sortOrder}
            onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
          >
            <option value="desc">Descending</option>
            <option value="asc">Ascending</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default ProductFilters;
