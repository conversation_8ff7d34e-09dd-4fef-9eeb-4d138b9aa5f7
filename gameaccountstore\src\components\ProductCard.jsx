import React from 'react';
import { Link } from 'react-router-dom';
import './ProductCard.css';

const ProductCard = ({ product }) => {
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  return (
    <div className="product-card">
      <Link to={`/product/${product.id}`} className="product-link">
        <div className="product-image">
          {product.screenshots && product.screenshots.length > 0 ? (
            <img 
              src={product.screenshots[0]} 
              alt={product.title}
              onError={(e) => {
                e.target.src = '/placeholder-image.jpg';
              }}
            />
          ) : (
            <div className="placeholder-image">
              <span>No Image</span>
            </div>
          )}
        </div>
        
        <div className="product-info">
          <h3 className="product-title">{product.title}</h3>
          <p className="product-game">{product.gameName}</p>
          <p className="product-description">
            {product.description.length > 100 
              ? `${product.description.substring(0, 100)}...` 
              : product.description
            }
          </p>
          
          <div className="product-meta">
            <div className="product-price">
              {formatPrice(product.price)}
            </div>
            <div className="product-seller">
              by {product.sellerName}
            </div>
          </div>
          
          <div className="product-footer">
            <span className="product-date">
              Listed {formatDate(product.createdAt)}
            </span>
            <span className="product-status available">
              Available
            </span>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default ProductCard;
