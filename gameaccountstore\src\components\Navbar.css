.navbar {
  background-color: #1a1a1a;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: bold;
  color: #4f46e5;
  text-decoration: none;
  transition: color 0.3s ease;
}

.navbar-brand:hover {
  color: #6366f1;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.navbar-item {
  color: #e5e7eb;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.navbar-item:hover {
  background-color: #374151;
  color: #ffffff;
}

.register-btn {
  background-color: #4f46e5;
  color: white;
}

.register-btn:hover {
  background-color: #4338ca;
}

.navbar-auth {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.navbar-user {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #e5e7eb;
}

.user-email {
  font-weight: 500;
}

.user-role {
  color: #9ca3af;
  font-size: 0.875rem;
}

.logout-btn {
  background-color: #dc2626;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.logout-btn:hover {
  background-color: #b91c1c;
}

@media (max-width: 768px) {
  .navbar-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .navbar-menu {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .navbar-user {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
}
