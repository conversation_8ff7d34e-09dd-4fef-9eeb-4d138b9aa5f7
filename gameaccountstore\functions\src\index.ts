import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
admin.initializeApp();

const db = admin.firestore();

/**
 * Cloud Function triggered when a new user is created
 * Sets custom claims based on the role specified in roleRequests collection
 */
export const onUserCreate = functions.auth.user().onCreate(async (user) => {
  try {
    // Check if there's a role request for this user
    const roleRequestRef = db.collection('roleRequests').doc(user.uid);
    const roleRequestDoc = await roleRequestRef.get();

    if (roleRequestDoc.exists) {
      const roleData = roleRequestDoc.data();
      const role = roleData?.role || 'buyer';

      // Set custom claims
      const customClaims: { [key: string]: any } = {
        role: role
      };

      // Set specific role claims
      if (role === 'admin') {
        customClaims.admin = true;
      } else if (role === 'seller') {
        customClaims.seller = true;
      } else {
        customClaims.buyer = true;
      }

      await admin.auth().setCustomUserClaims(user.uid, customClaims);

      // Mark role request as processed
      await roleRequestRef.update({
        processed: true,
        processedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log(`Custom claims set for user ${user.uid} with role: ${role}`);
    } else {
      // Default to buyer role if no role request found
      await admin.auth().setCustomUserClaims(user.uid, {
        role: 'buyer',
        buyer: true
      });

      console.log(`Default buyer role set for user ${user.uid}`);
    }
  } catch (error) {
    console.error('Error setting custom claims:', error);
  }
});

/**
 * Callable function to create an order and update product status
 * This ensures atomic transaction for purchase process
 */
export const createOrder = functions.https.onCall(async (data, context) => {
  // Check if user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'User must be authenticated to create an order.'
    );
  }

  const { productId, buyerId, sellerId, amount, productTitle } = data;

  // Validate input data
  if (!productId || !buyerId || !sellerId || !amount || !productTitle) {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'Missing required order data.'
    );
  }

  // Ensure the authenticated user is the buyer
  if (context.auth.uid !== buyerId) {
    throw new functions.https.HttpsError(
      'permission-denied',
      'User can only create orders for themselves.'
    );
  }

  try {
    // Use a transaction to ensure atomicity
    const result = await db.runTransaction(async (transaction) => {
      const productRef = db.collection('products').doc(productId);
      const productDoc = await transaction.get(productRef);

      if (!productDoc.exists) {
        throw new functions.https.HttpsError(
          'not-found',
          'Product not found.'
        );
      }

      const productData = productDoc.data();

      // Check if product is still available
      if (productData?.status !== 'available') {
        throw new functions.https.HttpsError(
          'failed-precondition',
          'Product is no longer available.'
        );
      }

      // Check if the seller is correct
      if (productData?.sellerId !== sellerId) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Invalid seller ID.'
        );
      }

      // Check if the price matches
      if (productData?.price !== amount) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Price mismatch.'
        );
      }

      // Create the order
      const orderData = {
        productId,
        buyerId,
        sellerId,
        productTitle,
        amount,
        purchaseDate: admin.firestore.FieldValue.serverTimestamp(),
        status: 'completed'
      };

      const orderRef = db.collection('orders').doc();
      transaction.set(orderRef, orderData);

      // Update product status to sold
      transaction.update(productRef, {
        status: 'sold',
        soldAt: admin.firestore.FieldValue.serverTimestamp(),
        soldTo: buyerId
      });

      return { orderId: orderRef.id };
    });

    console.log(`Order created successfully: ${result.orderId}`);
    return { success: true, orderId: result.orderId };

  } catch (error) {
    console.error('Error creating order:', error);
    
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    
    throw new functions.https.HttpsError(
      'internal',
      'An error occurred while processing the order.'
    );
  }
});

/**
 * Function to clean up old role requests (optional)
 * Can be triggered by a scheduled function
 */
export const cleanupRoleRequests = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async (context) => {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 7); // 7 days ago

      const oldRequestsQuery = db.collection('roleRequests')
        .where('processed', '==', true)
        .where('processedAt', '<', cutoffDate);

      const snapshot = await oldRequestsQuery.get();
      
      const batch = db.batch();
      snapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      
      console.log(`Cleaned up ${snapshot.size} old role requests`);
    } catch (error) {
      console.error('Error cleaning up role requests:', error);
    }
  });
