import React, { createContext, useContext, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { onAuthStateChanged } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import { setUser, setUserRole, setLoading, logout } from '../store/slices/authSlice';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const dispatch = useDispatch();
  const { user, userRole, loading } = useSelector((state) => state.auth);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          // Get user document from Firestore
          const userDocRef = doc(db, 'users', firebaseUser.uid);
          const userDoc = await getDoc(userDocRef);
          
          if (userDoc.exists()) {
            const userData = userDoc.data();
            dispatch(setUser({
              uid: firebaseUser.uid,
              email: firebaseUser.email,
              ...userData
            }));
            dispatch(setUserRole(userData.role));
          } else {
            // User document doesn't exist, create one with default role
            dispatch(setUser({
              uid: firebaseUser.uid,
              email: firebaseUser.email,
            }));
            dispatch(setUserRole('buyer')); // Default role
          }

          // Get custom claims for role verification
          const idTokenResult = await firebaseUser.getIdTokenResult();
          if (idTokenResult.claims.role) {
            dispatch(setUserRole(idTokenResult.claims.role));
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          dispatch(logout());
        }
      } else {
        dispatch(logout());
      }
      dispatch(setLoading(false));
    });

    return () => unsubscribe();
  }, [dispatch]);

  const value = {
    user,
    userRole,
    loading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
