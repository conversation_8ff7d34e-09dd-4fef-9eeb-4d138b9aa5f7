import React, { useState } from 'react';
import { collection, addDoc, updateDoc, doc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { db, storage } from '../config/firebase';
import { useAuth } from '../context/AuthContext';
import './ProductForm.css';

const ProductForm = ({ product, onClose }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    title: product?.title || '',
    description: product?.description || '',
    gameName: product?.gameName || '',
    price: product?.price || '',
  });
  const [screenshots, setScreenshots] = useState([]);
  const [video, setVideo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleScreenshotsChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 7) {
      setError('Maximum 7 screenshots allowed');
      return;
    }
    setScreenshots(files);
    setError('');
  };

  const handleVideoChange = (e) => {
    const file = e.target.files[0];
    if (file && file.size > 50 * 1024 * 1024) { // 50MB limit
      setError('Video file must be less than 50MB');
      return;
    }
    setVideo(file);
    setError('');
  };

  const uploadFiles = async () => {
    const uploadedScreenshots = [];
    let uploadedVideoUrl = '';

    // Upload screenshots
    for (let i = 0; i < screenshots.length; i++) {
      const file = screenshots[i];
      const fileName = `products/${user.uid}/${Date.now()}_screenshot_${i}`;
      const storageRef = ref(storage, fileName);
      
      try {
        const snapshot = await uploadBytes(storageRef, file);
        const downloadURL = await getDownloadURL(snapshot.ref);
        uploadedScreenshots.push(downloadURL);
      } catch (error) {
        console.error('Error uploading screenshot:', error);
        throw new Error(`Failed to upload screenshot ${i + 1}`);
      }
    }

    // Upload video if provided
    if (video) {
      const fileName = `products/${user.uid}/${Date.now()}_video`;
      const storageRef = ref(storage, fileName);
      
      try {
        const snapshot = await uploadBytes(storageRef, video);
        uploadedVideoUrl = await getDownloadURL(snapshot.ref);
      } catch (error) {
        console.error('Error uploading video:', error);
        throw new Error('Failed to upload video');
      }
    }

    return { uploadedScreenshots, uploadedVideoUrl };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Validation
      if (!formData.title || !formData.description || !formData.gameName || !formData.price) {
        throw new Error('All fields are required');
      }

      if (parseFloat(formData.price) <= 0) {
        throw new Error('Price must be greater than 0');
      }

      if (!product && screenshots.length === 0) {
        throw new Error('At least one screenshot is required');
      }

      let screenshotUrls = product?.screenshots || [];
      let videoUrl = product?.videoUrl || '';

      // Upload new files if provided
      if (screenshots.length > 0 || video) {
        const { uploadedScreenshots, uploadedVideoUrl } = await uploadFiles();
        if (uploadedScreenshots.length > 0) {
          screenshotUrls = uploadedScreenshots;
        }
        if (uploadedVideoUrl) {
          videoUrl = uploadedVideoUrl;
        }
      }

      const productData = {
        title: formData.title,
        description: formData.description,
        gameName: formData.gameName,
        price: parseFloat(formData.price),
        screenshots: screenshotUrls,
        videoUrl: videoUrl,
        sellerId: user.uid,
        sellerName: user.username || user.email,
        status: 'available',
      };

      if (product) {
        // Update existing product
        await updateDoc(doc(db, 'products', product.id), productData);
      } else {
        // Create new product
        productData.createdAt = new Date();
        await addDoc(collection(db, 'products'), productData);
      }

      onClose();
    } catch (error) {
      console.error('Error saving product:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="product-form-overlay">
      <div className="product-form-container">
        <div className="form-header">
          <h2>{product ? 'Edit Product' : 'Add New Product'}</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="product-form">
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <div className="form-group">
            <label htmlFor="title">Product Title *</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
              placeholder="Enter product title"
            />
          </div>

          <div className="form-group">
            <label htmlFor="gameName">Game Name *</label>
            <input
              type="text"
              id="gameName"
              name="gameName"
              value={formData.gameName}
              onChange={handleChange}
              required
              placeholder="Enter game name"
            />
          </div>

          <div className="form-group">
            <label htmlFor="price">Price (USD) *</label>
            <input
              type="number"
              id="price"
              name="price"
              value={formData.price}
              onChange={handleChange}
              required
              min="0.01"
              step="0.01"
              placeholder="0.00"
            />
          </div>

          <div className="form-group">
            <label htmlFor="description">Description *</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
              rows="4"
              placeholder="Describe the game account..."
            />
          </div>

          <div className="form-group">
            <label htmlFor="screenshots">
              Screenshots {!product && '*'} (Max 7 images)
            </label>
            <input
              type="file"
              id="screenshots"
              multiple
              accept="image/*"
              onChange={handleScreenshotsChange}
              {...(!product && { required: true })}
            />
            {screenshots.length > 0 && (
              <p className="file-info">{screenshots.length} file(s) selected</p>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="video">Video (Optional, Max 50MB)</label>
            <input
              type="file"
              id="video"
              accept="video/*"
              onChange={handleVideoChange}
            />
            {video && (
              <p className="file-info">{video.name} selected</p>
            )}
          </div>

          <div className="form-actions">
            <button type="button" onClick={onClose} className="cancel-btn">
              Cancel
            </button>
            <button type="submit" disabled={loading} className="submit-btn">
              {loading ? 'Saving...' : product ? 'Update Product' : 'Add Product'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProductForm;
