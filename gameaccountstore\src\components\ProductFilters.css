.product-filters {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  overflow: hidden;
}

.filters-header {
  padding: 1rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-filters-btn {
  background-color: #4f46e5;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.toggle-filters-btn:hover {
  background-color: #4338ca;
}

.reset-filters-btn {
  background-color: #6b7280;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.reset-filters-btn:hover {
  background-color: #4b5563;
}

.filters-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.filters-content.show {
  max-height: 300px;
  padding: 1rem;
}

.filters-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.filter-group input,
.filter-group select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.price-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.price-range input {
  flex: 1;
  min-width: 0;
}

.price-range span {
  color: #6b7280;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .filters-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .filters-content {
    grid-template-columns: 1fr;
  }
  
  .price-range {
    flex-direction: column;
    align-items: stretch;
  }
  
  .price-range span {
    text-align: center;
  }
}
