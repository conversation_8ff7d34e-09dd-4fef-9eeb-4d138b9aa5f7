rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return request.auth.token.role;
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserRole() == 'admin';
    }
    
    function isSeller() {
      return isAuthenticated() && getUserRole() == 'seller';
    }
    
    function isBuyer() {
      return isAuthenticated() && getUserRole() == 'buyer';
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Users collection rules
    match /users/{userId} {
      // Users can read and write their own document
      allow read, write: if isOwner(userId);
      // Admins can read all user documents
      allow read: if isAdmin();
      // Allow creation during registration
      allow create: if isAuthenticated() && isOwner(userId);
    }
    
    // Products collection rules
    match /products/{productId} {
      // Anyone can read products (for browsing)
      allow read: if true;
      
      // Only sellers can create products
      allow create: if isSeller() && 
        isOwner(resource.data.sellerId) &&
        resource.data.status == 'available';
      
      // Sellers can update their own products
      allow update: if isSeller() && 
        isOwner(resource.data.sellerId) &&
        // Prevent changing sellerId
        resource.data.sellerId == request.auth.uid;
      
      // Admins can update any product
      allow update: if isAdmin();
      
      // Sellers can delete their own products (if not sold)
      allow delete: if isSeller() && 
        isOwner(resource.data.sellerId) &&
        resource.data.status == 'available';
      
      // Admins can delete any product
      allow delete: if isAdmin();
    }
    
    // Orders collection rules
    match /orders/{orderId} {
      // Buyers can read their own orders
      allow read: if isAuthenticated() && 
        (isOwner(resource.data.buyerId) || 
         isOwner(resource.data.sellerId));
      
      // Admins can read all orders
      allow read: if isAdmin();
      
      // Orders are created only through Cloud Functions
      allow create: if false;
      
      // Orders cannot be updated or deleted by users
      allow update, delete: if false;
    }
    
    // Role requests collection (for user registration)
    match /roleRequests/{userId} {
      // Users can create role requests for themselves
      allow create: if isAuthenticated() && isOwner(userId);
      
      // Users can read their own role requests
      allow read: if isAuthenticated() && isOwner(userId);
      
      // Only Cloud Functions can update role requests
      allow update: if false;
      
      // Users cannot delete role requests
      allow delete: if false;
    }
    
    // Deny all other operations
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
