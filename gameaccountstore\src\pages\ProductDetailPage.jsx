import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { doc, getDoc } from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import { db, functions } from '../config/firebase';
import { useAuth } from '../context/AuthContext';
import ImageGallery from '../components/ImageGallery';
import './ProductDetailPage.css';

const ProductDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const productDoc = await getDoc(doc(db, 'products', id));
        if (productDoc.exists()) {
          setProduct({ id: productDoc.id, ...productDoc.data() });
        } else {
          setError('Product not found');
        }
      } catch (error) {
        console.error('Error fetching product:', error);
        setError('Failed to load product');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  const handlePurchase = async () => {
    if (!user) {
      navigate('/login');
      return;
    }

    if (product.status !== 'available') {
      setError('This product is no longer available');
      return;
    }

    setPurchasing(true);
    setError('');

    try {
      const createOrder = httpsCallable(functions, 'createOrder');
      const result = await createOrder({
        productId: product.id,
        buyerId: user.uid,
        sellerId: product.sellerId,
        amount: product.price,
        productTitle: product.title
      });

      if (result.data.success) {
        alert('Purchase successful! You will receive the account details via email.');
        navigate('/');
      } else {
        setError(result.data.error || 'Purchase failed');
      }
    } catch (error) {
      console.error('Purchase error:', error);
      setError('Failed to complete purchase. Please try again.');
    } finally {
      setPurchasing(false);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">Loading product...</div>
      </div>
    );
  }

  if (error && !product) {
    return (
      <div className="error-container">
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => navigate('/')} className="back-button">
          Back to Home
        </button>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="error-container">
        <h2>Product Not Found</h2>
        <p>The product you're looking for doesn't exist.</p>
        <button onClick={() => navigate('/')} className="back-button">
          Back to Home
        </button>
      </div>
    );
  }

  return (
    <div className="product-detail-page">
      <div className="product-detail-container">
        <button onClick={() => navigate('/')} className="back-button">
          ← Back to Products
        </button>

        <div className="product-detail-content">
          <div className="product-media">
            <ImageGallery 
              images={product.screenshots || []} 
              videoUrl={product.videoUrl}
            />
          </div>

          <div className="product-info">
            <div className="product-header">
              <h1>{product.title}</h1>
              <div className="product-game">{product.gameName}</div>
              <div className="product-price">{formatPrice(product.price)}</div>
            </div>

            <div className="product-status-section">
              <span className={`status-badge ${product.status}`}>
                {product.status === 'available' ? 'Available' : 'Sold'}
              </span>
              <span className="listed-date">
                Listed on {formatDate(product.createdAt)}
              </span>
            </div>

            <div className="product-description">
              <h3>Description</h3>
              <p>{product.description}</p>
            </div>

            <div className="seller-info">
              <h3>Seller Information</h3>
              <p>Sold by: <strong>{product.sellerName}</strong></p>
            </div>

            {error && (
              <div className="error-message">
                {error}
              </div>
            )}

            <div className="purchase-section">
              {product.status === 'available' ? (
                <button 
                  onClick={handlePurchase}
                  disabled={purchasing || !user}
                  className="purchase-button"
                >
                  {purchasing ? 'Processing...' : 
                   !user ? 'Login to Purchase' : 
                   `Purchase for ${formatPrice(product.price)}`}
                </button>
              ) : (
                <button disabled className="purchase-button sold">
                  This account has been sold
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
