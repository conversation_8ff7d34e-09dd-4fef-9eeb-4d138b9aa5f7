.App {
  min-height: 100vh;
  background-color: #f9fafb;
}

.main-content {
  min-height: calc(100vh - 80px);
}

/* Global loading and error styles */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner {
  font-size: 1.125rem;
  color: #4f46e5;
  font-weight: 500;
}

.error-container {
  text-align: center;
  padding: 3rem 1rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #dc2626;
  max-width: 600px;
  margin: 2rem auto;
}

.error-container h2 {
  color: #dc2626;
  margin: 0 0 1rem 0;
}

.error-container p {
  color: #6b7280;
  margin: 0 0 1rem 0;
}

.access-denied {
  text-align: center;
  padding: 3rem 1rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #f59e0b;
  max-width: 600px;
  margin: 2rem auto;
}

.access-denied h2 {
  color: #f59e0b;
  margin: 0 0 1rem 0;
}

.access-denied p {
  color: #6b7280;
  margin: 0.5rem 0;
}
