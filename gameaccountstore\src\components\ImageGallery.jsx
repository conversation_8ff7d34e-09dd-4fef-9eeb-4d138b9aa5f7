import React, { useState } from 'react';
import './ImageGallery.css';

const ImageGallery = ({ images = [], videoUrl }) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [showVideo, setShowVideo] = useState(false);

  const allMedia = [...images];
  if (videoUrl) {
    allMedia.push({ type: 'video', url: videoUrl });
  }

  if (allMedia.length === 0) {
    return (
      <div className="image-gallery">
        <div className="no-media">
          <span>No images or videos available</span>
        </div>
      </div>
    );
  }

  const currentMedia = allMedia[selectedIndex];
  const isVideo = currentMedia?.type === 'video';

  return (
    <div className="image-gallery">
      <div className="main-media">
        {isVideo ? (
          <video 
            controls 
            className="main-video"
            key={currentMedia.url}
          >
            <source src={currentMedia.url} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        ) : (
          <img 
            src={currentMedia} 
            alt={`Product image ${selectedIndex + 1}`}
            className="main-image"
            onError={(e) => {
              e.target.src = '/placeholder-image.jpg';
            }}
          />
        )}
      </div>

      {allMedia.length > 1 && (
        <div className="media-thumbnails">
          {allMedia.map((media, index) => (
            <div
              key={index}
              className={`thumbnail ${selectedIndex === index ? 'active' : ''}`}
              onClick={() => setSelectedIndex(index)}
            >
              {media.type === 'video' ? (
                <div className="video-thumbnail">
                  <span>▶</span>
                  <span>Video</span>
                </div>
              ) : (
                <img 
                  src={media} 
                  alt={`Thumbnail ${index + 1}`}
                  onError={(e) => {
                    e.target.src = '/placeholder-image.jpg';
                  }}
                />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ImageGallery;
