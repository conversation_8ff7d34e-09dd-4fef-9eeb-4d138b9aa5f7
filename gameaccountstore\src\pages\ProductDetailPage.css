.product-detail-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.product-detail-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.back-button {
  background-color: #6b7280;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  align-self: flex-start;
}

.back-button:hover {
  background-color: #4b5563;
}

.product-detail-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.product-media {
  display: flex;
  flex-direction: column;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.product-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.product-game {
  color: #4f46e5;
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 1rem;
}

.product-price {
  font-size: 2rem;
  font-weight: 700;
  color: #059669;
}

.product-status-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
  border-top: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
}

.status-badge.available {
  background-color: #d1fae5;
  color: #065f46;
}

.status-badge.sold {
  background-color: #fee2e2;
  color: #991b1b;
}

.listed-date {
  color: #6b7280;
  font-size: 0.875rem;
}

.product-description h3,
.seller-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.product-description p {
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

.seller-info p {
  color: #4b5563;
  margin: 0;
}

.error-message {
  background-color: #fee2e2;
  color: #991b1b;
  padding: 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  border: 1px solid #fecaca;
}

.purchase-section {
  margin-top: auto;
}

.purchase-button {
  width: 100%;
  background-color: #4f46e5;
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.purchase-button:hover:not(:disabled) {
  background-color: #4338ca;
}

.purchase-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.purchase-button.sold {
  background-color: #dc2626;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  font-size: 1.125rem;
  color: #4f46e5;
  font-weight: 500;
}

.error-container h2 {
  color: #dc2626;
  margin: 0 0 1rem 0;
}

.error-container p {
  color: #6b7280;
  margin: 0 0 1rem 0;
}

@media (max-width: 768px) {
  .product-detail-page {
    padding: 1rem;
  }
  
  .product-detail-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 1rem;
  }
  
  .product-header h1 {
    font-size: 1.5rem;
  }
  
  .product-price {
    font-size: 1.5rem;
  }
  
  .product-status-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
